"""
调试命令处理模块
提供调试命令的解析和执行功能
"""

import os
import logging
import json
import threading
from datetime import datetime
from typing import List, Dict, Tuple, Any, Optional, Callable
from modules.memory.content_generator import ContentGenerator  # 导入内容生成服务

logger = logging.getLogger('main')

class DebugCommandHandler:
    """调试命令处理器类，处理各种调试命令"""

    def __init__(self, root_dir: str, memory_service=None, llm_service=None, content_generator=None):
        """
        初始化调试命令处理器

        Args:
            root_dir: 项目根目录
            memory_service: 记忆服务实例
            llm_service: LLM服务实例
            content_generator: 内容生成服务实例
        """
        self.root_dir = root_dir
        self.memory_service = memory_service
        self.llm_service = llm_service
        self.content_generator = content_generator
        self.DEBUG_PREFIX = "/"

        # 如果没有提供内容生成服务，尝试初始化
        if not self.content_generator:
            try:
                from src.config import config
                self.content_generator = ContentGenerator(
                    root_dir=self.root_dir,
                    api_key=config.OPENAI_API_KEY,
                    base_url=config.OPENAI_API_BASE,
                    model=config.OPENAI_API_MODEL,
                    max_token=config.OPENAI_MAX_TOKENS,
                    temperature=config.OPENAI_TEMPERATURE,
                    memory_manager=self.memory_service
                )
                logger.info("内容生成服务初始化成功")
            except Exception as e:
                logger.error(f"初始化内容生成服务失败: {str(e)}")
                self.content_generator = None

    def is_debug_command(self, message: str) -> bool:
        """
        判断消息是否为调试命令

        Args:
            message: 用户消息

        Returns:
            bool: 是否为调试命令
        """
        return message.strip().startswith(self.DEBUG_PREFIX)

    def process_command(self, command: str, current_avatar: str, user_id: str, chat_id: str = None, callback: Callable = None) -> Tuple[bool, str]:
        """
        处理调试命令

        Args:
            command: 调试命令（包含/前缀）
            current_avatar: 当前角色名
            user_id: 用户ID
            chat_id: 聊天ID，用于异步回调
            callback: 回调函数，用于异步处理生成的内容

        Returns:
            Tuple[bool, str]: (是否需要拦截普通消息处理, 响应消息)
        """
        # 去除前缀并转为小写
        cmd = command.strip()[1:].lower()

        # 帮助命令
        if cmd == "help":
            return True, self._get_help_message()

        # 显示当前角色记忆
        elif cmd == "mem":
            return True, self._show_memory(current_avatar, user_id)

        # 重置当前角色的最近记忆
        elif cmd == "reset":
            return True, self._reset_short_memory(current_avatar, user_id)

        # 清空当前角色的核心记忆
        elif cmd == "clear":
            return True, self._clear_core_memory(current_avatar, user_id)

        # 清空当前角色的对话上下文
        elif cmd == "context":
            return True, self._clear_context(user_id)
        
        # 手动生成核心记忆
        elif cmd == "gen_core_mem":
            return True, self._gen_core_mem(current_avatar, user_id)

        # 内容生成命令，如果提供了回调函数，则使用异步方式
        elif cmd in ["diary", "state", "letter", "list", "pyq", "gift", "shopping"]:
            if callback and chat_id:
                # 使用异步方式生成内容
                return True, self._generate_content_async(cmd, current_avatar, user_id, chat_id, callback)
            else:
                # 使用同步方式生成内容
                return True, self._generate_content(cmd, current_avatar, user_id)

        # 查看所有人设
        elif cmd == "avatar":
            return True, self._show_all_avatars()

        # 查看当前人设
        elif cmd == "now":
            return True, self._show_current_avatar(current_avatar)

        # 切换人设命令
        elif cmd.startswith("switch "):
            avatar_name = cmd[7:].strip()  # 去除"switch "前缀
            if not avatar_name:
                return True, "请指定要切换的人设名称，格式: /switch 人设名"
            return True, self._switch_avatar(avatar_name)

        # 记忆同步命令
        elif cmd.startswith("git"):
            if cmd == "git":
                return True, "请指定源人设和目标人设，格式: /git 源人设名 目标人设名"
            elif cmd.startswith("git "):
                parts = cmd[4:].strip().split()  # 去除"git "前缀并分割参数
                if len(parts) != 2:
                    return True, "请指定源人设和目标人设，格式: /git 源人设名 目标人设名"
                source_avatar, target_avatar = parts
                return True, self._sync_avatar_memory(source_avatar, target_avatar, user_id)
            else:
                return True, f"未知命令: {cmd}\n使用 /help 查看可用命令"

        # 退出调试模式
        elif cmd == "exit":
            return True, "已退出调试模式"

        # 无效命令
        else:
            return True, f"未知命令: {cmd}\n使用 /help 查看可用命令"

    def _get_help_message(self) -> str:
        """获取帮助信息"""
        return """调试模式命令:
- /help: 显示此帮助信息
- /mem: 显示当前角色的记忆
- /reset: 重置当前角色的最近记忆
- /clear: 清空当前角色的核心记忆
- /context: 清空当前角色的对话上下文
- /diary: 生成角色小日记
- /state: 查看角色状态
- /letter: 角色给你写的信
- /list: 角色的备忘录
- /pyq: 角色的朋友圈
- /gift: 角色想送的礼物
- /shopping: 角色的购物清单
- /avatar: 查看所有可用人设
- /now: 查看当前人设
- /switch 人设名: 切换到指定人设
- /git 源人设名 目标人设名: 将源人设的记忆同步到目标人设
- /exit: 退出调试模式"""

    def _gen_core_mem(self, avatar_name: str, user_id: str) -> str:
        if not self.memory_service:
            return f"错误: 记忆服务未初始化"

        context = self.memory_service.get_recent_context(avatar_name, user_id)
        if self.memory_service.update_core_memory(avatar_name, user_id, context):
            return f"成功更新核心记忆"
        else:
            return f"未能成功更新核心记忆"

    def _show_memory(self, avatar_name: str, user_id: str) -> str:
        """
        显示当前角色的记忆

        Args:
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 记忆内容
        """
        if not self.memory_service:
            return "错误: 记忆服务未初始化"

        try:
            # 获取核心记忆
            core_memory = self.memory_service.get_core_memory(avatar_name, user_id)
            if not core_memory:
                core_memory_str = "当前角色没有核心记忆"
            else:
                core_memory_str = core_memory

            # 获取短期记忆（最近对话上下文）
            recent_context = self.memory_service.get_recent_context(avatar_name, user_id, 5)
            if not recent_context:
                short_memory_str = "当前角色没有短期记忆"
            else:
                # 格式化短期记忆
                short_memory_items = []
                for i in range(0, len(recent_context), 2):
                    if i + 1 < len(recent_context):
                        user_msg = recent_context[i].get('content', '')
                        bot_msg = recent_context[i + 1].get('content', '')
                        short_memory_items.append(f"用户: {user_msg}\n回复: {bot_msg}")

                short_memory_str = "\n\n".join(short_memory_items)

            return f"核心记忆:\n{core_memory_str}\n\n短期记忆:\n{short_memory_str}"

        except Exception as e:
            logger.error(f"获取记忆失败: {str(e)}")
            return f"获取记忆失败: {str(e)}"

    def _reset_short_memory(self, avatar_name: str, user_id: str) -> str:
        """
        重置当前角色的最近记忆

        Args:
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 操作结果
        """
        if not self.memory_service:
            return "错误: 记忆服务未初始化"

        try:
            # 使用记忆管理器的统一接口重置短期记忆
            if self.memory_service.reset_short_memory(avatar_name, user_id):
                # 同时清除LLM服务中的对话上下文，避免重新加载旧记忆
                if self.llm_service:
                    self.llm_service.clear_history(user_id)
                    logger.info(f"已清除用户 {user_id} 的LLM对话上下文")
                return f"已重置 {avatar_name} 的最近记忆"
            else:
                return f"重置 {avatar_name} 的最近记忆失败"
        except Exception as e:
            logger.error(f"重置最近记忆失败: {str(e)}")
            return f"重置最近记忆失败: {str(e)}"

    def _clear_core_memory(self, avatar_name: str, user_id: str) -> str:
        """
        清空当前角色的核心记忆

        Args:
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 操作结果
        """
        if not self.memory_service:
            return "错误: 记忆服务未初始化"

        try:
            # 使用记忆管理器的统一接口清空核心记忆
            if self.memory_service.clear_core_memory(avatar_name, user_id):
                # 同时清除LLM服务中的对话上下文，避免重新加载旧记忆
                if self.llm_service:
                    self.llm_service.clear_history(user_id)
                    logger.info(f"已清除用户 {user_id} 的LLM对话上下文")
                return f"已清空 {avatar_name} 的核心记忆"
            else:
                return f"清空 {avatar_name} 的核心记忆失败"
        except Exception as e:
            logger.error(f"清空核心记忆失败: {str(e)}")
            return f"清空核心记忆失败: {str(e)}"

    def _clear_context(self, user_id: str) -> str:
        """
        清空当前角色的对话上下文

        Args:
            user_id: 用户ID

        Returns:
            str: 操作结果
        """
        if not self.llm_service:
            return "错误: LLM服务未初始化"

        try:
            self.llm_service.clear_history(user_id)
            return "已清空对话上下文"
        except Exception as e:
            logger.error(f"清空对话上下文失败: {str(e)}")
            return f"清空对话上下文失败: {str(e)}"

    def _generate_content(self, content_type: str, avatar_name: str, user_id: str) -> str:
        """
        通用内容生成方法

        Args:
            content_type: 内容类型，如 'diary', 'state', 'letter'
            avatar_name: 角色名
            user_id: 用户ID

        Returns:
            str: 生成的内容
        """
        if not self.content_generator:
            return "错误: 内容生成服务未初始化"

        try:
            # 根据内容类型调用相应的方法
            content_type_methods = {
                'diary': self.content_generator.generate_diary,
                'state': self.content_generator.generate_state,
                'letter': self.content_generator.generate_letter,
                'list': self.content_generator.generate_list,
                'pyq': self.content_generator.generate_pyq,
                'gift': self.content_generator.generate_gift,
                'shopping': self.content_generator.generate_shopping
            }

            # 获取并使用相应的生成方法，或使用默认方法
            generate_method = content_type_methods.get(content_type)
            if not generate_method:
                return f"不支持的内容类型: {content_type}"

            content = generate_method(avatar_name, user_id)

            if not content or content.startswith("无法"):
                return content

            logger.info(f"已生成{avatar_name}的{content_type} 用户: {user_id}")
            return content

        except Exception as e:
            logger.error(f"生成{content_type}失败: {str(e)}")
            return f"{content_type}生成失败: {str(e)}"

    def _generate_content_async(self, content_type: str, avatar_name: str, user_id: str, chat_id: str, callback: Callable[[str, str, str], None]) -> str:
        """
        异步生成内容

        Args:
            content_type: 内容类型，如 'diary', 'state', 'letter'
            avatar_name: 角色名
            user_id: 用户ID
            chat_id: 聊天ID，用于回调发送消息
            callback: 回调函数，用于处理生成的内容

        Returns:
            str: 初始响应消息
        """
        if not self.content_generator:
            return "错误: 内容生成服务未初始化"

        # 创建异步线程执行内容生成
        def generate_thread():
            try:
                # 根据内容类型调用相应的方法
                content_type_methods = {
                    'diary': self.content_generator.generate_diary,
                    'state': self.content_generator.generate_state,
                    'letter': self.content_generator.generate_letter,
                    'list': self.content_generator.generate_list,
                    'pyq': self.content_generator.generate_pyq,
                    'gift': self.content_generator.generate_gift,
                    'shopping': self.content_generator.generate_shopping
                }

                # 获取并使用相应的生成方法，或使用默认方法
                generate_method = content_type_methods.get(content_type)
                if not generate_method:
                    result = f"不支持的内容类型: {content_type}"
                    callback(command=f"/{content_type}", reply=result, chat_id=chat_id)
                    return

                # 生成内容
                content = generate_method(avatar_name, user_id)

                if not content or content.startswith("无法"):
                    callback(command=f"/{content_type}", reply=content, chat_id=chat_id)
                    return

                logger.info(f"已生成{avatar_name}的{content_type} 用户: {user_id}")
                # 调用回调函数处理生成的内容
                callback(command=f"/{content_type}", reply=content, chat_id=chat_id)

            except Exception as e:
                error_msg = f"{content_type}生成失败: {str(e)}"
                logger.error(error_msg)
                callback(command=f"/{content_type}", reply=error_msg, chat_id=chat_id)

        # 启动异步线程
        thread = threading.Thread(target=generate_thread)
        thread.daemon = True  # 设置为守护线程，不会阻止程序退出
        thread.start()

        # 静默生成，不返回任何初始响应
        return ""

    def _generate_diary(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的日记"""
        return self._generate_content('diary', avatar_name, user_id)

    def _generate_state(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的状态信息"""
        return self._generate_content('state', avatar_name, user_id)

    def _generate_letter(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色给用户写的信"""
        return self._generate_content('letter', avatar_name, user_id)

    def _generate_list(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的备忘录"""
        return self._generate_content('list', avatar_name, user_id)

    def _generate_pyq(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的朋友圈"""
        return self._generate_content('pyq', avatar_name, user_id)

    def _generate_gift(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色想送的礼物"""
        return self._generate_content('gift', avatar_name, user_id)

    def _generate_shopping(self, avatar_name: str, user_id: str) -> str:
        """生成当前角色的购物清单"""
        return self._generate_content('shopping', avatar_name, user_id)

    def _show_all_avatars(self) -> str:
        """显示所有可用的人设"""
        try:
            avatars_dir = os.path.join(self.root_dir, "data", "avatars")

            if not os.path.exists(avatars_dir):
                return "人设目录不存在"

            avatars = []
            for item in os.listdir(avatars_dir):
                item_path = os.path.join(avatars_dir, item)
                # 检查是否为目录且包含avatar.md文件
                if os.path.isdir(item_path):
                    avatar_file = os.path.join(item_path, "avatar.md")
                    if os.path.exists(avatar_file):
                        avatars.append(item)

            if not avatars:
                return "未找到任何可用的人设"

            avatars.sort()  # 按字母顺序排序
            avatar_list = "\n".join([f"- {avatar}" for avatar in avatars])
            return f"可用的人设列表:\n{avatar_list}\n\n使用 /switch 人设名 来切换人设"

        except Exception as e:
            logger.error(f"获取人设列表失败: {str(e)}")
            return f"获取人设列表失败: {str(e)}"

    def _show_current_avatar(self, current_avatar: str) -> str:
        """显示当前人设信息"""
        try:
            avatar_path = os.path.join(self.root_dir, "data", "avatars", current_avatar, "avatar.md")

            if not os.path.exists(avatar_path):
                return f"当前人设文件不存在: {current_avatar}"

            # 读取人设文件的基本信息
            with open(avatar_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取角色名称（从"# 角色"部分）
            role_info = ""
            lines = content.split('\n')
            in_role_section = False

            for line in lines:
                if line.strip().startswith('# 角色'):
                    in_role_section = True
                    continue
                elif line.strip().startswith('# ') and in_role_section:
                    break
                elif in_role_section and line.strip():
                    role_info += line.strip() + " "

            if role_info:
                role_info = role_info.strip()[:100] + "..." if len(role_info) > 100 else role_info.strip()
            else:
                role_info = "无角色描述"

            return f"当前人设: {current_avatar}\n角色信息: {role_info}"

        except Exception as e:
            logger.error(f"获取当前人设信息失败: {str(e)}")
            return f"获取当前人设信息失败: {str(e)}"

    def _switch_avatar(self, avatar_name: str) -> str:
        """切换到指定人设"""
        try:
            # 检查人设是否存在
            avatar_path = os.path.join(self.root_dir, "data", "avatars", avatar_name, "avatar.md")

            if not os.path.exists(avatar_path):
                return f"人设不存在: {avatar_name}\n使用 /avatar 查看可用人设"

            # 导入必要的模块和函数
            from src.main import switch_avatar

            # 执行人设切换
            switch_avatar(avatar_name)

            logger.info(f"通过调试命令切换人设到: {avatar_name}")
            return f"已成功切换到人设: {avatar_name}"

        except Exception as e:
            logger.error(f"切换人设失败: {str(e)}")
            return f"切换人设失败: {str(e)}"

    def _sync_avatar_memory(self, source_avatar: str, target_avatar: str, user_id: str) -> str:
        """
        将源人设的记忆同步到目标人设

        Args:
            source_avatar: 源人设名称
            target_avatar: 目标人设名称
            user_id: 用户ID

        Returns:
            str: 操作结果消息
        """
        try:
            # 检查源人设是否存在
            source_avatar_path = os.path.join(self.root_dir, "data", "avatars", source_avatar, "avatar.md")
            if not os.path.exists(source_avatar_path):
                return f"源人设不存在: {source_avatar}\n使用 /avatar 查看可用人设"

            # 检查目标人设是否存在
            target_avatar_path = os.path.join(self.root_dir, "data", "avatars", target_avatar, "avatar.md")
            if not os.path.exists(target_avatar_path):
                return f"目标人设不存在: {target_avatar}\n使用 /avatar 查看可用人设"

            if not self.memory_service:
                return "错误: 记忆服务未初始化"

            logger.info(f"开始同步记忆: {source_avatar} -> {target_avatar}, 用户: {user_id}")

            # 获取源人设的记忆数据
            source_core_memory = self.memory_service.get_core_memory(source_avatar, user_id)
            source_short_memory = self.memory_service.get_recent_context(source_avatar, user_id, 50)

            # 检查是否有记忆数据需要同步
            if not source_core_memory and not source_short_memory:
                return f"源人设 {source_avatar} 没有记忆数据可同步"

            # 执行记忆同步
            sync_result = self._perform_memory_sync(
                source_avatar, target_avatar, user_id,
                source_core_memory, source_short_memory
            )

            if sync_result:
                # 同步成功后，重新加载目标人设的记忆
                self._reload_target_avatar_memory(target_avatar, user_id)

                logger.info(f"记忆同步成功: {source_avatar} -> {target_avatar}")
                return f"成功将 {source_avatar} 的记忆同步到 {target_avatar}\n" \
                       f"已同步核心记忆: {'是' if source_core_memory else '否'}\n" \
                       f"已同步短期记忆: {len(source_short_memory) if source_short_memory else 0} 条对话"
            else:
                return f"记忆同步失败: {source_avatar} -> {target_avatar}"

        except Exception as e:
            logger.error(f"记忆同步过程中发生错误: {str(e)}")
            return f"记忆同步失败: {str(e)}"

    def _perform_memory_sync(self, source_avatar: str, target_avatar: str, user_id: str,
                           source_core_memory: str, source_short_memory: list) -> bool:
        """
        执行实际的记忆同步操作

        Args:
            source_avatar: 源人设名称
            target_avatar: 目标人设名称
            user_id: 用户ID
            source_core_memory: 源人设的核心记忆
            source_short_memory: 源人设的短期记忆

        Returns:
            bool: 同步是否成功
        """
        try:
            # 检查记忆服务类型，支持新旧记忆系统
            from src.config import config

            if hasattr(self.memory_service, 'class_name') and self.memory_service.class_name == "MemoryManager":
                # 新的记忆管理器
                return self._sync_with_memory_manager(
                    source_avatar, target_avatar, user_id,
                    source_core_memory, source_short_memory
                )
            else:
                # 旧的记忆服务
                return self._sync_with_old_memory_service(
                    source_avatar, target_avatar, user_id,
                    source_core_memory, source_short_memory
                )

        except Exception as e:
            logger.error(f"执行记忆同步失败: {str(e)}")
            return False

    def _sync_with_memory_manager(self, source_avatar: str, target_avatar: str, user_id: str,
                                source_core_memory: str, source_short_memory: list) -> bool:
        """
        使用新的记忆管理器进行同步
        """
        try:
            # 同步核心记忆
            if source_core_memory:
                # 直接设置目标人设的核心记忆
                success = self._copy_core_memory_to_target(source_core_memory, target_avatar, user_id)
                if not success:
                    logger.warning(f"核心记忆同步失败: {source_avatar} -> {target_avatar}")

            # 同步短期记忆
            if source_short_memory:
                success = self._copy_short_memory_to_target(source_short_memory, target_avatar, user_id)
                if not success:
                    logger.warning(f"短期记忆同步失败: {source_avatar} -> {target_avatar}")

            return True

        except Exception as e:
            logger.error(f"使用记忆管理器同步失败: {str(e)}")
            return False

    def _sync_with_old_memory_service(self, source_avatar: str, target_avatar: str, user_id: str,
                                    source_core_memory: str, source_short_memory: list) -> bool:
        """
        使用旧的记忆服务进行同步
        """
        try:
            # 同步核心记忆
            if source_core_memory:
                success = self._copy_core_memory_to_target(source_core_memory, target_avatar, user_id)
                if not success:
                    logger.warning(f"核心记忆同步失败: {source_avatar} -> {target_avatar}")

            # 同步短期记忆
            if source_short_memory:
                success = self._copy_short_memory_to_target(source_short_memory, target_avatar, user_id)
                if not success:
                    logger.warning(f"短期记忆同步失败: {source_avatar} -> {target_avatar}")

            return True

        except Exception as e:
            logger.error(f"使用旧记忆服务同步失败: {str(e)}")
            return False

    def _copy_core_memory_to_target(self, source_core_memory: str, target_avatar: str, user_id: str) -> bool:
        """
        将核心记忆复制到目标人设，支持新旧记忆系统
        """
        try:
            import json
            from datetime import datetime
            from src.config import config

            # 检查记忆系统类型
            if config.memory_and_story.memory_system_type == "database":
                # 新记忆系统：同时写入MD和数据库
                return self._copy_core_memory_to_database(source_core_memory, target_avatar, user_id)
            else:
                # 旧记忆系统：写入JSON文件
                return self._copy_core_memory_to_json(source_core_memory, target_avatar, user_id)

        except Exception as e:
            logger.error(f"复制核心记忆失败: {str(e)}")
            return False

    def _copy_core_memory_to_json(self, source_core_memory: str, target_avatar: str, user_id: str) -> bool:
        """
        将核心记忆复制到JSON文件（旧记忆系统）
        """
        try:
            import json
            from datetime import datetime

            # 获取目标人设的核心记忆文件路径
            target_memory_dir = os.path.join(self.root_dir, "data", "avatars", target_avatar, "memory", user_id)
            os.makedirs(target_memory_dir, exist_ok=True)

            target_core_memory_path = os.path.join(target_memory_dir, "core_memory.json")

            # 创建新的核心记忆数据
            core_memory_data = {
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "content": source_core_memory
            }

            # 写入目标人设的核心记忆文件
            with open(target_core_memory_path, "w", encoding="utf-8") as f:
                json.dump(core_memory_data, f, ensure_ascii=False, indent=2)

            logger.info(f"核心记忆已复制到JSON文件: {target_core_memory_path}")
            return True

        except Exception as e:
            logger.error(f"复制核心记忆到JSON文件失败: {str(e)}")
            return False

    def _copy_core_memory_to_database(self, source_core_memory: str, target_avatar: str, user_id: str) -> bool:
        """
        将核心记忆复制到数据库系统（新记忆系统）
        """
        try:
            from datetime import datetime

            # 创建目标人设的记忆目录结构
            target_memory_dir = os.path.join(self.root_dir, "data", "avatars", target_avatar, "newmemory")
            target_md_dir = os.path.join(target_memory_dir, "md")
            os.makedirs(target_md_dir, exist_ok=True)

            # 写入MD文件
            target_core_memory_md = os.path.join(target_md_dir, "core_memory.md")
            with open(target_core_memory_md, "w", encoding="utf-8") as f:
                f.write(f"# 用户: {user_id}\n\n{source_core_memory}\n")

            logger.info(f"核心记忆已复制到MD文件: {target_core_memory_md}")

            # 触发数据库同步
            if hasattr(self.memory_service, 'sync_initializer') and self.memory_service.sync_initializer:
                self.memory_service.sync_initializer.sync_specific_avatar(target_avatar)
                logger.info(f"已触发目标人设 {target_avatar} 的数据库同步")

            return True

        except Exception as e:
            logger.error(f"复制核心记忆到数据库失败: {str(e)}")
            return False

    def _copy_short_memory_to_target(self, source_short_memory: list, target_avatar: str, user_id: str) -> bool:
        """
        将短期记忆复制到目标人设，支持新旧记忆系统
        """
        try:
            from src.config import config

            # 检查记忆系统类型
            if config.memory_and_story.memory_system_type == "database":
                # 新记忆系统：同时写入MD和数据库
                return self._copy_short_memory_to_database(source_short_memory, target_avatar, user_id)
            else:
                # 旧记忆系统：写入JSON文件
                return self._copy_short_memory_to_json(source_short_memory, target_avatar, user_id)

        except Exception as e:
            logger.error(f"复制短期记忆失败: {str(e)}")
            return False

    def _copy_short_memory_to_json(self, source_short_memory: list, target_avatar: str, user_id: str) -> bool:
        """
        将短期记忆复制到JSON文件（旧记忆系统）
        """
        try:
            import json
            from datetime import datetime

            # 获取目标人设的短期记忆文件路径
            target_memory_dir = os.path.join(self.root_dir, "data", "avatars", target_avatar, "memory", user_id)
            os.makedirs(target_memory_dir, exist_ok=True)

            target_short_memory_path = os.path.join(target_memory_dir, "short_memory.json")

            # 转换短期记忆格式（从LLM格式转换为存储格式）
            converted_memory = []
            for i in range(0, len(source_short_memory), 2):
                if i + 1 < len(source_short_memory):
                    user_msg = source_short_memory[i]
                    bot_msg = source_short_memory[i + 1]

                    if user_msg.get('role') == 'user' and bot_msg.get('role') == 'assistant':
                        converted_memory.append({
                            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                            "user": user_msg.get('content', ''),
                            "bot": bot_msg.get('content', '')
                        })

            # 写入目标人设的短期记忆文件
            with open(target_short_memory_path, "w", encoding="utf-8") as f:
                json.dump(converted_memory, f, ensure_ascii=False, indent=2)

            logger.info(f"短期记忆已复制到JSON文件: {target_short_memory_path}, 共 {len(converted_memory)} 条对话")
            return True

        except Exception as e:
            logger.error(f"复制短期记忆到JSON文件失败: {str(e)}")
            return False

    def _copy_short_memory_to_database(self, source_short_memory: list, target_avatar: str, user_id: str) -> bool:
        """
        将短期记忆复制到数据库系统（新记忆系统）
        """
        try:
            from datetime import datetime

            # 创建目标人设的记忆目录结构
            target_memory_dir = os.path.join(self.root_dir, "data", "avatars", target_avatar, "newmemory")
            target_md_dir = os.path.join(target_memory_dir, "md")
            os.makedirs(target_md_dir, exist_ok=True)

            # 转换短期记忆格式并写入MD文件
            target_short_memory_md = os.path.join(target_md_dir, "short_memory.md")

            md_content = f"# 短期记忆 - 用户: {user_id}\n\n"

            # 转换短期记忆格式
            for i in range(0, len(source_short_memory), 2):
                if i + 1 < len(source_short_memory):
                    user_msg = source_short_memory[i]
                    bot_msg = source_short_memory[i + 1]

                    if user_msg.get('role') == 'user' and bot_msg.get('role') == 'assistant':
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                        md_content += f"## {timestamp}\n\n"
                        md_content += f"**用户**: {user_msg.get('content', '')}\n\n"
                        md_content += f"**回复**: {bot_msg.get('content', '')}\n\n"
                        md_content += "---\n\n"

            # 写入MD文件
            with open(target_short_memory_md, "w", encoding="utf-8") as f:
                f.write(md_content)

            logger.info(f"短期记忆已复制到MD文件: {target_short_memory_md}")

            # 触发数据库同步
            if hasattr(self.memory_service, 'sync_initializer') and self.memory_service.sync_initializer:
                self.memory_service.sync_initializer.sync_specific_avatar(target_avatar)
                logger.info(f"已触发目标人设 {target_avatar} 的数据库同步")

            return True

        except Exception as e:
            logger.error(f"复制短期记忆到数据库失败: {str(e)}")
            return False

    def _reload_target_avatar_memory(self, target_avatar: str, user_id: str):
        """
        重新加载目标人设的记忆，确保同步后的记忆能够被正确读取
        """
        try:
            # 如果当前正在使用目标人设，需要清除LLM服务的缓存
            if hasattr(self, 'llm_service') and self.llm_service:
                self.llm_service.clear_history(user_id)
                logger.info(f"已清除用户 {user_id} 的LLM对话上下文缓存")

            # 如果使用的是记忆管理器，触发记忆重新加载
            if hasattr(self.memory_service, 'class_name') and self.memory_service.class_name == "MemoryManager":
                # 对于新记忆系统，可能需要触发数据库同步
                if hasattr(self.memory_service, 'sync_initializer') and self.memory_service.sync_initializer:
                    self.memory_service.sync_initializer.sync_specific_avatar(target_avatar)
                    logger.info(f"已触发目标人设 {target_avatar} 的数据库同步")

            # 初始化目标人设的记忆文件（如果需要）
            self.memory_service.initialize_avatar_memory(target_avatar, user_id)

            logger.info(f"目标人设 {target_avatar} 的记忆已重新加载")

        except Exception as e:
            logger.error(f"重新加载目标人设记忆失败: {str(e)}")
            # 即使重新加载失败，也不影响同步操作的成功
