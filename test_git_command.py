#!/usr/bin/env python3
"""
测试 /git 命令功能的脚本
"""

import os
import sys
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.handlers.debug import DebugCommandHandler
from modules.memory.memory_service import MemoryService

def create_test_memory_data():
    """创建测试用的记忆数据"""
    # 创建测试源人设的记忆目录
    source_avatar = "2t2"
    user_id = "test_user"
    
    memory_dir = os.path.join("data", "avatars", source_avatar, "oldmemory", user_id)
    os.makedirs(memory_dir, exist_ok=True)
    
    # 创建核心记忆
    core_memory_data = {
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
        "content": "这是测试的核心记忆内容。用户喜欢编程和人工智能。"
    }
    
    core_memory_path = os.path.join(memory_dir, "core_memory.json")
    with open(core_memory_path, "w", encoding="utf-8") as f:
        json.dump(core_memory_data, f, ensure_ascii=False, indent=2)
    
    # 创建短期记忆
    short_memory_data = [
        {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user": "你好，我想了解一下人工智能",
            "bot": "你好！我很乐意和你聊聊人工智能。你想了解哪个方面呢？"
        },
        {
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "user": "机器学习和深度学习有什么区别？",
            "bot": "机器学习是一个更广泛的概念，而深度学习是机器学习的一个子集..."
        }
    ]
    
    short_memory_path = os.path.join(memory_dir, "short_memory.json")
    with open(short_memory_path, "w", encoding="utf-8") as f:
        json.dump(short_memory_data, f, ensure_ascii=False, indent=2)
    
    print(f"已创建测试记忆数据: {memory_dir}")

def test_git_command():
    """测试 /git 命令"""
    try:
        # 创建测试记忆数据
        create_test_memory_data()
        
        # 初始化记忆服务
        root_dir = os.path.dirname(os.path.abspath(__file__))
        memory_service = MemoryService(
            root_dir=root_dir,
            api_key="test_key",
            base_url="test_url",
            model="test_model",
            max_token=4000,
            temperature=0.7,
            max_groups=10
        )
        
        # 初始化调试命令处理器
        debug_handler = DebugCommandHandler(
            root_dir=root_dir,
            memory_service=memory_service
        )
        
        # 测试 /git 命令
        print("\n=== 测试 /git 命令 ===")
        
        # 测试参数不足的情况
        intercept, response = debug_handler.process_command(
            command="/git",
            current_avatar="2t2",
            user_id="test_user"
        )
        print(f"参数不足测试: {response}")
        
        # 测试正常的记忆同步
        intercept, response = debug_handler.process_command(
            command="/git 2t2 若叶睦",
            current_avatar="2t2",
            user_id="test_user"
        )
        print(f"记忆同步测试: {response}")
        
        # 检查目标人设是否有了记忆数据
        target_memory_dir = os.path.join("data", "avatars", "若叶睦", "memory", "test_user")
        if os.path.exists(target_memory_dir):
            print(f"目标记忆目录已创建: {target_memory_dir}")
            
            core_memory_path = os.path.join(target_memory_dir, "core_memory.json")
            if os.path.exists(core_memory_path):
                with open(core_memory_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    print(f"目标核心记忆: {data['content'][:50]}...")
            
            short_memory_path = os.path.join(target_memory_dir, "short_memory.json")
            if os.path.exists(short_memory_path):
                with open(short_memory_path, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    print(f"目标短期记忆条数: {len(data)}")
        
        print("\n=== 测试完成 ===")
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_git_command()
