#!/usr/bin/env python3
"""
测试记忆优化功能
验证缓存和综合查询是否正常工作
"""

import os
import sys
import logging
import tempfile
import shutil
from datetime import datetime

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from modules.newmemory.database_memory_service import DatabaseMemoryService
from modules.memory_manager import MemoryManager

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_memory_optimization():
    """测试记忆优化功能"""
    
    # 创建临时目录
    temp_dir = tempfile.mkdtemp()
    logger.info(f"使用临时目录: {temp_dir}")
    
    try:
        # 初始化数据库记忆服务
        memory_service = DatabaseMemoryService(
            root_dir=temp_dir,
            api_key="test_key",
            base_url="test_url", 
            model="test_model",
            max_token=1000,
            temperature=0.7,
            max_groups=15
        )
        
        # 测试数据
        avatar_name = "test_avatar"
        user_id = "test_user"
        
        logger.info("=== 开始测试记忆优化功能 ===")
        
        # 1. 添加一些测试对话
        logger.info("1. 添加测试对话...")
        for i in range(10):
            user_message = f"用户消息 {i+1}"
            bot_reply = f"机器人回复 {i+1}"
            memory_service.add_conversation(avatar_name, user_message, bot_reply, user_id)
        
        # 2. 第一次查询（应该从数据库查询）
        logger.info("2. 第一次查询综合记忆...")
        result1 = memory_service.get_comprehensive_memory(avatar_name, user_id, 50)
        logger.info(f"短期记忆条数: {len(result1['short_memory'])}")
        logger.info(f"核心记忆长度: {len(result1['core_memory'])}")
        
        # 3. 第二次查询（应该从缓存获取）
        logger.info("3. 第二次查询综合记忆（测试缓存）...")
        result2 = memory_service.get_comprehensive_memory(avatar_name, user_id, 50)
        logger.info(f"短期记忆条数: {len(result2['short_memory'])}")
        logger.info(f"核心记忆长度: {len(result2['core_memory'])}")
        
        # 4. 验证结果一致性
        logger.info("4. 验证缓存结果一致性...")
        if result1['short_memory'] == result2['short_memory']:
            logger.info("✓ 短期记忆缓存一致性测试通过")
        else:
            logger.error("✗ 短期记忆缓存一致性测试失败")
            
        if result1['core_memory'] == result2['core_memory']:
            logger.info("✓ 核心记忆缓存一致性测试通过")
        else:
            logger.error("✗ 核心记忆缓存一致性测试失败")
        
        # 5. 添加新对话，测试缓存失效
        logger.info("5. 添加新对话，测试缓存失效...")
        memory_service.add_conversation(avatar_name, "新用户消息", "新机器人回复", user_id)
        
        # 6. 再次查询（缓存应该失效，重新从数据库查询）
        logger.info("6. 缓存失效后查询...")
        result3 = memory_service.get_comprehensive_memory(avatar_name, user_id, 50)
        logger.info(f"短期记忆条数: {len(result3['short_memory'])}")
        
        # 验证新对话是否包含在结果中
        if len(result3['short_memory']) > len(result1['short_memory']):
            logger.info("✓ 缓存失效测试通过，新对话已包含")
        else:
            logger.error("✗ 缓存失效测试失败")
        
        # 7. 测试缓存统计
        logger.info("7. 缓存统计信息...")
        logger.info(f"当前缓存条目数: {len(memory_service.cache)}")
        
        logger.info("=== 记忆优化功能测试完成 ===")
        
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 清理临时目录
        try:
            shutil.rmtree(temp_dir)
            logger.info(f"已清理临时目录: {temp_dir}")
        except Exception as e:
            logger.warning(f"清理临时目录失败: {e}")

if __name__ == "__main__":
    test_memory_optimization()
